'use strict'
const BaseAdapter = require('./base-adapter')
const StateManager = require('../state-manager')
const DatabaseManager = require('../database-manager')

/**
 * 租号玩平台适配器
 * 实现租号玩平台的具体接口调用逻辑
 */
class ZuhaoWanAdapter extends BaseAdapter {
  constructor(config) {
    super(config)
    this.baseUrl = 'https://zu.zuhaowan.com'
    this.loginUrl = 'https://zu.zuhaowan.com/api/Login/login'
  }

  /**
   * 登录租号玩平台
   * @returns {Promise<Object>} 登录结果
   */
  async login() {
    try {
      if (!this.username || !this.password) {
        throw new Error('缺少用户名或密码')
      }
      
      const loginData = {
        username: this.username,
        password: this.password,
        type: '2' // 2表示员工登录
      }
      console.log('登录数据:', loginData)
      
      const response = await this.request(this.loginUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        },
        data: this.buildFormData(loginData)
      })
      
      if (response.code === 200 && response.data && response.data.token) {
        this.token = response.data.token
        // {{ AURA-X: Modify - 使用配置ID精确更新，避免误更新多个同类型配置. Approval: 寸止(ID:1735373000). }}
        // 更新数据库中的token
        const dbManager = new DatabaseManager()
        if (this.config._id) {
          // 使用配置ID精确更新
          await dbManager.updatePlatformConfigById(this.config._id, {
            token: this.token,
            login_status: 1,
            last_login_time: new Date()
          })
        } else {
          // 兼容旧版本：使用原有方法
          await dbManager.updatePlatformConfig(this.config.user_id, this.platformType, {
            token: this.token,
            login_status: 1,
            last_login_time: new Date()
          })
        }
        return {
          success: true,
          token: this.token,
          message: response.message || '登录成功'
        }
      } else {
        console.log('租号玩登录失败，响应:', JSON.stringify(response))
        throw new Error(response.message || '登录失败')
      }
    } catch (error) {
      console.error('租号玩登录失败:', error)
      // {{ AURA-X: Modify - 使用DatabaseManager统一更新配置. Approval: 寸止(ID:**********). }}
      // 更新登录状态为失效
      const dbManager = new DatabaseManager()
      await dbManager.updatePlatformConfig(this.config.user_id, this.platformType, {
        login_status: 2
      })
      return {
        success: false,
        token: null,
        message: error.message
      }
    }
  }

  // {{ AURA-X: Delete - 移除冗余方法，登录状态检测已移至基类统一处理. Approval: 寸止(ID:**********). }}
  // checkLoginStatus方法已移除，登录状态检测在基类的executeRequest中统一处理

  /**
   * 检查登录是否过期 (重写父类方法)
   * @param {Object} responseData 响应数据
   * @returns {boolean} 是否登录过期
   */
  isLoginExpired(responseData) {
    console.log('租号玩登录状态检测:', responseData)
    // {{ AURA-X: Add - 租号玩登录状态检测逻辑. Approval: 寸止(ID:**********). }}
    return responseData.code == 401
  }

  // {{ AURA-X: Delete - 移除空的token刷新方法，使用基类默认实现. Approval: 寸止(ID:**********). }}
  // checkAndRefreshToken方法已移除，使用基类的默认实现

  /**
   * 检查是否是认证错误 (重写父类方法)
   * @param {Error} error 错误对象
   * @returns {boolean} 是否是认证错误
   */
  isAuthError(error) {
    return super.isAuthError(error) ||
           (error.response && error.response.data && error.response.data.code === 401)
  }

  /**
   * 获取货架列表
   * @returns {Promise<Array>} 货架列表
   */
  async getShelfList() {
    try {
      const response = await this.request(`${this.baseUrl}/api/Account/search`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'Token': this.token
        },
        data: this.buildFormData({
          status: '', // 空表示获取全部状态
          pagesize: '100',
          pageindex: '1'
        })
      })
      if (response.code === 200 && response.data && response.data.list) {
        return response.data.list.map(item => this.convertShelfData(item))
      } else {
        throw new Error(response.message || '获取货架列表失败')
      }
    } catch (error) {
      console.error('获取租号玩货架列表失败:', error)
      throw error
    }
  }

  /**
   * 上架货架
   * @param {string} shelfId 货架ID
   * @returns {Promise<Object>} 操作结果
   */
  async onShelf(shelfId) {
    try {
      const response = await this.request(`${this.baseUrl}/api/Account/onRent`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'Token': this.token
        },
        data: this.buildFormData({
          id: shelfId
        })
      })
      if (response.code === 200) {
        return {
          success: true,
          message: response.message || '上架成功'
        }
      } else {
        throw new Error(response.message || '上架失败')
      }
    } catch (error) {
      console.error('租号玩上架失败:', error)
      return {
        success: false,
        message: error.message
      }
    }
  }

  /**
   * 下架货架
   * @param {string} shelfId 货架ID
   * @returns {Promise<Object>} 操作结果
   */
  async offShelf(shelfId) {
    try {
      const response = await this.request(`${this.baseUrl}/api/Account/offRent`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'Token': this.token
        },
        data: this.buildFormData({
          id: shelfId
        })
      })
      if (response.code === 200) {
        return {
          success: true,
          message: response.message || '下架成功'
        }
      } else {
        throw new Error(response.message || '下架失败')
      }
    } catch (error) {
      console.error('租号玩下架失败:', error)
      return {
        success: false,
        message: error.message
      }
    }
  }

  // {{ AURA-X: Delete - 移除冗余方法，直接使用基类的getShelfStatusFromList. Approval: 寸止(ID:**********). }}
  // getShelfStatus方法已移除，直接使用基类的getShelfStatusFromList方法

  /**
   * 转换货架数据格式
   * @param {Object} rawData 原始数据
   * @returns {Object} 标准化数据
   */
  convertShelfData(rawData) {
    return {
      id: rawData.id.toString(),
      game_account: rawData.zh,
      game_name: rawData.game_name,
      game_role_name: rawData.game_zone_name,
      shelf_title: rawData.pn,
      rent_price: parseFloat(rawData.pmoney),
      min_rent_time: rawData.szq,
      unified_state: StateManager.convertZuhaoWanStatus(rawData.zt),
      platform_status: rawData
    }
  }

  /**
   * 构建表单数据
   * @param {Object} data 数据对象
   * @returns {string} 表单数据字符串
   */
  buildFormData(data) {
    const formData = new URLSearchParams()
    for (const key in data) {
      formData.append(key, data[key])
    }
    return formData.toString()
  }

  // {{ AURA-X: Delete - 移除重复的数据库操作，使用DatabaseManager统一处理. Approval: 寸止(ID:**********). }}
  // updatePlatformConfig方法已移除，使用DatabaseManager.updatePlatformConfig
}

module.exports = ZuhaoWanAdapter
