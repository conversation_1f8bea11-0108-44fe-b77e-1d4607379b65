/**
 * 前端统一状态管理器
 * {{ AURA-X: Modify - 与后端状态管理器完全统一，减少重复代码. Approval: 寸止(ID:1735372900). }}
 * 与后端状态管理器保持一致
 */
export const StateManager = {
  // 统一状态定义
  STATES: {
    AVAILABLE: 0,    // 待租
    RENTED: 1,       // 出租中
    OFFLINE: -1,     // 下架
    OTHER: -2        // 其他
  },

  // 状态文本映射 - 使用动态键以保持与后端一致
  STATE_TEXTS: {
    [0]: '待租',
    [1]: '出租中',
    [-1]: '已下架',
    [-2]: '其他'
  },

  // 状态样式类映射 - 使用动态键以保持与后端一致
  STATE_CLASSES: {
    [0]: 'available',
    [1]: 'rented',
    [-1]: 'offline',
    [-2]: 'unknown'
  },

  /**
   * 获取状态文本
   * @param {number} state 统一状态码
   * @returns {string} 状态文本
   */
  getStateText(state) {
    return this.STATE_TEXTS[state] || '其他'
  },

  /**
   * 获取状态样式类
   * @param {number} state 统一状态码
   * @returns {string} 样式类名
   */
  getStateClass(state) {
    return this.STATE_CLASSES[state] || 'unknown'
  },

  /**
   * 检查状态是否可以上架
   * @param {number} state 统一状态码
   * @returns {boolean} 是否可以上架
   */
  canOnShelf(state) {
    return state === this.STATES.OFFLINE
  },

  /**
   * 检查状态是否可以下架
   * @param {number} state 统一状态码
   * @returns {boolean} 是否可以下架
   */
  canOffShelf(state) {
    return state === this.STATES.AVAILABLE
  },

  /**
   * 获取操作按钮文本
   * @param {number} state 统一状态码
   * @returns {string} 按钮文本
   */
  getActionButtonText(state) {
    if (this.canOffShelf(state)) {
      return '下架'
    } else if (this.canOnShelf(state)) {
      return '上架'
    } else {
      return '不可操作'
    }
  },

  /**
   * 获取目标状态
   * @param {number} currentState 当前状态
   * @returns {number} 目标状态
   */
  getTargetState(currentState) {
    if (this.canOffShelf(currentState)) {
      return this.STATES.OFFLINE
    } else if (this.canOnShelf(currentState)) {
      return this.STATES.AVAILABLE
    } else {
      throw new Error('当前状态不支持切换操作')
    }
  },

  /**
   * 验证状态码是否有效
   * @param {number} state 状态码
   * @returns {boolean} 是否有效
   */
  isValidState(state) {
    return Object.values(this.STATES).includes(state)
  },

  /**
   * 检查状态是否可以上架
   * @param {number} state 统一状态码
   * @returns {boolean} 是否可以上架
   */
  canOnShelf(state) {
    return state === this.STATES.OFFLINE
  },

  /**
   * 检查状态是否可以下架
   * @param {number} state 统一状态码
   * @returns {boolean} 是否可以下架
   */
  canOffShelf(state) {
    return state === this.STATES.AVAILABLE
  },

  /**
   * 获取所有状态定义
   * @returns {Object} 状态定义对象
   */
  getAllStates() {
    return { ...this.STATES }
  },

  /**
   * 租号玩状态转换
   * @param {number} ztStatus 租号玩状态码
   * @returns {number} 统一状态码
   */
  convertZuhaoWanStatus(ztStatus) {
    switch (ztStatus) {
      case 0:
        return this.STATES.AVAILABLE  // 待租
      case 1:
        return this.STATES.RENTED     // 出租中
      case -1:
        return this.STATES.OFFLINE    // 下架
      default:
        return this.STATES.OTHER      // 其他
    }
  },

  /**
   * U号租状态转换
   * @param {number} goodsStatus 商品状态
   * @param {number} rentStatus 租赁状态
   * @returns {number} 统一状态码
   */
  convertUhaoZuStatus(goodsStatus, rentStatus) {
    if (goodsStatus === 4) {
      // 下架状态
      return this.STATES.OFFLINE
    } else if (goodsStatus === 3) {
      // 上架状态，需要根据租赁状态判断
      if (rentStatus === 0) {
        // 出租中
        return this.STATES.RENTED
      } else {
        // 待租
        return this.STATES.AVAILABLE
      }
    } else {
      // 其他
      return this.STATES.OTHER
    }
  },

  /**
   * 获取状态统计信息
   * @param {Array} shelfList 货架列表
   * @returns {Object} 统计信息
   */
  getStateStats(shelfList) {
    const stats = {
      total: shelfList.length,
      available: 0,
      rented: 0,
      offline: 0,
      other: 0
    }

    shelfList.forEach(shelf => {
      switch (shelf.unified_state) {
        case this.STATES.AVAILABLE:
          stats.available++
          break
        case this.STATES.RENTED:
          stats.rented++
          break
        case this.STATES.OFFLINE:
          stats.offline++
          break
        case this.STATES.OTHER:
          stats.other++
          break
      }
    })

    return stats
  },

  /**
   * 过滤指定状态的货架
   * @param {Array} shelfList 货架列表
   * @param {string} filterType 过滤类型
   * @returns {Array} 过滤后的列表
   */
  filterShelfsByState(shelfList, filterType) {
    switch (filterType) {
      case 'available':
        return shelfList.filter(shelf => shelf.unified_state === this.STATES.AVAILABLE)
      case 'rented':
        return shelfList.filter(shelf => shelf.unified_state === this.STATES.RENTED)
      case 'offline':
        return shelfList.filter(shelf => shelf.unified_state === this.STATES.OFFLINE)
      case 'other':
        return shelfList.filter(shelf => shelf.unified_state === this.STATES.OTHER)
      default:
        return shelfList
    }
  }
}

export default StateManager