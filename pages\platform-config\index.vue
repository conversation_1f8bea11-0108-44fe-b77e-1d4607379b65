<template>
  <view class="container">
    <!-- 顶部操作栏 -->
    <view class="header">
      <view class="header-title">平台配置管理</view>
      <uv-button 
        type="primary" 
        size="small" 
        @click="goToAdd"
        icon="plus"
        iconSize="20"
        iconColor="#fff"
        :customStyle="{
          borderRadius: '100px',
          padding: '0 20rpx'
        }"
        text="添加平台"
      >
      </uv-button>
    </view>
    <!-- 统计信息 -->
    <view class="stats">
      <view class="stat-item">
        <text class="stat-value">{{ platformList.length }}</text>
        <text class="stat-label">总配置</text>
      </view>
      <view class="stat-item">
        <text class="stat-value">{{ activeCount }}</text>
        <text class="stat-label">已激活</text>
      </view>
      <view class="stat-item">
        <text class="stat-value">{{ loginCount }}</text>
        <text class="stat-label">已登录</text>
      </view>
    </view>
    <!-- 平台列表 -->
    <view class="platform-list">
      <view
        v-for="platform in platformList"
        :key="platform._id"
        class="platform-item"
      >
        <view class="platform-info">
          <view class="platform-header">
            <text class="platform-name">{{ platform.platform_name }}</text>
            <view class="platform-status">
              <text
                class="status-tag"
                :class="getStatusClass(platform.login_status)"
              >
                {{ getStatusText(platform.login_status) }}
              </text>
            </view>
          </view>
          <view class="platform-details">
            <view class="detail-item">
              <text class="detail-label">用户名：</text>
              <text class="detail-value">{{ platform.username || '未配置' }}</text>
            </view>
            <view class="detail-item">
              <text class="detail-label">登录方式：</text>
              <text class="detail-value">{{ platform.auto_login ? '自动登录' : '手动配置' }}</text>
            </view>
            <view class="detail-item">
              <text class="detail-label">最后登录：</text>
              <text class="detail-value">{{ formatTime(platform.last_login_time) }}</text>
            </view>
            <view class="detail-item" v-if="platform.remark">
              <text class="detail-label">备注：</text>
              <text class="detail-value">{{ platform.remark }}</text>
            </view>
          </view>
        </view>
        <view class="platform-actions">
          <uv-button
            type="info"
            size="small"
            @click="editPlatform(platform)"
            :customStyle="{
              borderRadius: '100px'
            }"
            text="编辑"
          >
          </uv-button>
          <uv-button
            type="primary"
            size="small"
            :loading="platform.testing"
            @click="testLogin(platform)"
            :customStyle="{
              borderRadius: '100px'
            }"
            :text="platform.testing ? '登录中' : '登录'"
          >
          </uv-button>
          <uv-button
            type="success"
            size="small"
            :loading="platform.syncing"
            @click="syncPlatform(platform)"
            :customStyle="{
              borderRadius: '100px'
            }"
            :text="platform.syncing ? '更新中' : '更新货架'"
          >
          </uv-button>
          <uv-button
            type="error"
            size="small"
            @click="deletePlatform(platform)"
            :customStyle="{
              borderRadius: '100px'
            }"
            text="删除"
          >
          </uv-button>
        </view>
      </view>
      <!-- 空状态 -->
      <view v-if="platformList.length === 0" class="empty-state">
        <text class="empty-icon">📋</text>
        <text class="empty-text">暂无平台配置</text>
        <text class="empty-desc">点击右上角添加平台开始使用</text>
      </view>
    </view>
    <!-- 加载状态 -->
    <view v-if="loading" class="loading">
      <uv-loading-icon mode="circle" size="40"></uv-loading-icon>
      <text style="margin-left: 20rpx;">加载中...</text>
    </view>

    <!-- 简化版tabBar -->
    <simple-tabbar :current="currentTabIndex" ></simple-tabbar>
  </view>
</template>
<script>
import { callFunction } from '@/utils/request.js'
import SimpleTabbar from '@/components/simple-tabbar/simple-tabbar.vue'
export default {
  name: 'PlatformConfig',
  components: {
    SimpleTabbar
  },
  data() {
    return {
      platformList: [],
      loading: false,
      currentTabIndex: 2
    }
  },
  computed: {
    activeCount() {
      return this.platformList.filter(p => p.status === 1).length
    },
    loginCount() {
      return this.platformList.filter(p => p.login_status === 1).length
    }
  },
  onShow() {
    this.loadPlatformList()
  },
  methods: {
    async loadPlatformList() {
      this.loading = true
      try {
        const result = await callFunction('shelf-management', {
          action: 'getPlatformConfigs'
        })
        if (result.code === 0) {
          this.platformList = result.data.map(platform => ({
            ...platform,
            testing: false,
            syncing: false
          }))
        } else {
          throw new Error(result.message)
        }
      } catch (error) {
        console.error('加载平台配置失败:', error)
        uni.showToast({
          title: '加载失败',
          icon: 'error'
        })
      } finally {
        this.loading = false
      }
    },
    goToAdd() {
      uni.navigateTo({
        url: '/pages/platform-config/add'
      })
    },
    editPlatform(platform) {
      uni.navigateTo({
        url: `/pages/platform-config/add?id=${platform._id}&type=edit`
      })
    },
    async testLogin(platform) {
      platform.testing = true
      try {
        const result = await callFunction('shelf-management', {
          action: 'testPlatformLogin',
          data: {
            configId: platform._id, // {{ AURA-X: Add - 传递配置ID用于精确更新. Approval: 寸止(ID:1735373000). }}
            platformType: platform.platform_type
            // {{ AURA-X: Delete - 移除多余参数，云函数通过configId自动获取完整配置. Approval: 寸止(ID:1735373100). }}
          }
        })
        if (result.code === 0) {
          uni.showToast({
            title: '登录成功',
            icon: 'success'
          })
          this.loadPlatformList()
        } else {
          uni.showModal({
            title: '登录失败',
            content: result.message,
            showCancel: false
          })
        }
      } catch (error) {
        console.error('登录失败:', error)
        uni.showToast({
          title: '登录失败',
          icon: 'error'
        })
      } finally {
        platform.testing = false
      }
    },
    async syncPlatform(platform) {
      platform.syncing = true
      try {
        const result = await callFunction('shelf-management', {
          action: 'syncShelves',
          data: {
            platformType: platform.platform_type
          }
        })
        if (result.code === 0) {
          uni.showToast({
            title: `更新成功，共${result.data.syncCount}个货架`,
            icon: 'success'
          })
        } else {
          uni.showModal({
            title: '更新失败',
            content: result.message,
            showCancel: false
          })
        }
      } catch (error) {
        console.error('更新货架失败:', error)
        uni.showToast({
          title: '更新失败',
          icon: 'error'
        })
      } finally {
        platform.syncing = false
      }
    },
    deletePlatform(platform) {
      uni.showModal({
        title: '确认删除',
        content: `确定要删除平台配置"${platform.platform_name}"吗？`,
        success: async (res) => {
          if (res.confirm) {
            try {
              const result = await callFunction('shelf-management', {
                action: 'deletePlatformConfig',
                data: {
                  configId: platform._id
                }
              })
              if (result.code === 0) {
                uni.showToast({
                  title: '删除成功',
                  icon: 'success'
                })
                this.loadPlatformList()
              } else {
                throw new Error(result.message)
              }
            } catch (error) {
              console.error('删除平台配置失败:', error)
              uni.showToast({
                title: '删除失败',
                icon: 'error'
              })
            }
          }
        }
      })
    },
    getStatusClass(status) {
      switch (status) {
        case 1: return 'success'
        case 2: return 'error'
        default: return 'warning'
      }
    },
    getStatusText(status) {
      switch (status) {
        case 1: return '已登录'
        case 2: return '登录失效'
        default: return '未登录'
      }
    },
    formatTime(timestamp) {
      if (!timestamp) return '从未登录'
      const date = new Date(timestamp)
      const now = new Date()
      const diff = now - date
      if (diff < 60000) {
        return '刚刚'
      } else if (diff < 3600000) {
        return Math.floor(diff / 60000) + '分钟前'
      } else if (diff < 86400000) {
        return Math.floor(diff / 3600000) + '小时前'
      } else if (diff < 2592000000) {
        return Math.floor(diff / 86400000) + '天前'
      } else {
        return date.toLocaleDateString()
      }
    }
  }
}
</script> 
<style lang="scss" scoped>
.container {
  padding: $spacing-base; /* 底部留出tabBar空间 */
  background: $bg-color-page;
  height: 100%;
  box-sizing: border-box;
  overflow-y: auto;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: $spacing-lg;
}

.header-title {
  font-size: $font-size-xl;
  font-weight: $font-weight-bold;
  color: $text-color-primary;
}

.stats {
  display: flex;
  gap: $spacing-sm;
  background: $bg-color-container;
  border-radius: $border-radius-lg;
  padding: $spacing-lg;
  box-shadow: $shadow-base;
  margin-bottom: $spacing-lg;
}

.stat-item {
  flex: 1;
  text-align: center;
}

.stat-value {
  font-size: $font-size-xxxl;
  font-weight: $font-weight-bold;
  color: $primary-color;
  margin-bottom: 8rpx;
  display: block;
}

.stat-label {
  font-size: $font-size-sm;
  color: $text-color-secondary;
  font-weight: $font-weight-normal;
}
.platform-list {
  display: flex;
  flex-direction: column;
}

.platform-item {
  background: $bg-color-container;
  border-radius: $border-radius-lg;
  padding: $spacing-xl;
  box-shadow: $shadow-base;
  margin-bottom: $spacing-base;
}
.platform-item:last-child {
  margin-bottom: 0;
}

.platform-info {
  margin-bottom: $spacing-base;
}

.platform-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: $spacing-base;
}

.platform-name {
  font-size: $font-size-lg;
  font-weight: $font-weight-semibold;
  color: $text-color-primary;
}

.status-tag {
  font-size: $font-size-xs;
  padding: 4rpx 12rpx;
  border-radius: $border-radius-base;
  font-weight: $font-weight-medium;
  display: inline-block;

  &.success {
    background-color: $success-light;
    color: $success-color;
  }

  &.error {
    background-color: $error-light;
    color: $error-color;
  }

  &.warning {
    background-color: $warning-light;
    color: $warning-color;
  }
}

.platform-details {
  margin-bottom: $spacing-base;
}

.detail-item {
  display: flex;
  align-items: center;
  min-width: 0;
  flex: 1;
  margin-bottom: 12rpx;
}

.detail-label {
  font-size: $font-size-sm;
  color: $text-color-secondary;
  margin-right: 8rpx;
  white-space: nowrap;
  width: 160rpx;
  flex-shrink: 0;
}

.detail-value {
  font-size: $font-size-sm;
  color: $text-color-primary;
  word-break: break-all;
}

.platform-actions {
  display: flex;
  flex-wrap: wrap;
  gap: $spacing-sm;
}

.empty-state {
  background: $bg-color-container;
  border-radius: $border-radius-lg;
  padding: $spacing-xl;
  box-shadow: $shadow-base;
  margin-bottom: $spacing-base;
  text-align: center;
}

.empty-icon {
  font-size: 120rpx;
  display: block;
  margin-bottom: $spacing-lg;
  color: $text-color-placeholder;
}

.empty-text {
  font-size: $font-size-lg;
  color: $text-color-primary;
  display: block;
  margin-bottom: $spacing-sm;
  font-weight: $font-weight-medium;
}

.empty-desc {
  font-size: $font-size-sm;
  color: $text-color-secondary;
  display: block;
}

.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: $spacing-lg;
  color: $text-color-secondary;
}
</style>