<template>
  <view class="simple-tabbar">
    <view
      v-for="(item, index) in tabs"
      :key="index"
      class="tab-item"
      :class="{ active: current === index }"
      @click="switchTab(index)"
    >
      <view class="tab-icon">
        <uv-icon
          :name="item.icon"
          :color="current === index ? '#3c9cff' : '#7A7E83'"
          size="44rpx"
        ></uv-icon>
      </view>
      <view
        class="tab-text"
        :style="{ color: current === index ? '#3c9cff' : '#7A7E83' }"
      >
        {{ item.text }}
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'SimpleTabbar',
  props: {
    current: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      tabs: [
        { text: '监控', icon: 'eye', path: 'pages/shelf-monitor/index' },
        { text: '货架', icon: 'grid', path: 'pages/shelf-list/index' },
        { text: '配置', icon: 'setting', path: 'pages/platform-config/index' },
        { text: '日志', icon: 'file-text', path: 'pages/operation-logs/index' }
      ]
    }
  },
  methods: {
    switchTab(index) {
      if (this.current === index) return

      const tab = this.tabs[index]
      this.$emit('change', { index, path: tab.path })

      uni.switchTab({
        url: `/${tab.path}`,
        fail: () => {
          uni.navigateTo({ url: `/${tab.path}` })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.simple-tabbar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 88rpx;
  background-color: #F8F8F8;
  border-top: 1rpx solid #e5e5e5;
  display: flex;
  z-index: 1;

  .tab-item {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 8rpx 0;

    .tab-icon {
      margin-bottom: 4rpx;
    }

    .tab-text {
      font-size: 20rpx;
    }
  }
}
</style>
