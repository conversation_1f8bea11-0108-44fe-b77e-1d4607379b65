import Vue from 'vue'
import Vuex from 'vuex'
import base from './modules/base' // 项目基础
Vue.use(Vuex)
// 持久化存储
const persistPlugin = store => {
  // 读取状态
  let state = uni.getStorageSync('state')
  if (state) {
    store.replaceState(state)
  }
  // 监听状态变化
  store.subscribe((mutation, state) => {
    uni.setStorageSync('state', state)
  })
}
export default new Vuex.Store({
  state: {
    
  },
  mutations: {
    
  },
  modules: {
    base
  },
  plugins: [persistPlugin]
})
