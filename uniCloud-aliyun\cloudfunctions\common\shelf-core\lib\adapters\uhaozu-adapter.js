'use strict'
const BaseAdapter = require('./base-adapter')
const StateManager = require('../state-manager')
const DatabaseManager = require('../database-manager')
const crypto = require('crypto')

/**
 * U号租平台适配器
 * 实现U号租平台的具体接口调用逻辑
 */
class UhaoZuAdapter extends BaseAdapter {
  constructor(config) {
    super(config)
    this.baseUrl = 'https://api.uhaozu.com'
    this.loginUrl = 'https://api.uhaozu.com/tool/user/access-token'
  }

  /**
   * 登录U号租平台
   * @returns {Promise<Object>} 登录结果
   */
  async login() {
    console.log('U号租登录')
    try {
      if (!this.username || !this.password) {
        throw new Error('缺少用户名或密码')
      }

      // U号租需要MD5加密密码
      const md5Password = crypto.createHash('md5').update(this.password).digest('hex')
      
      const loginData = {
        from: '',
        password: md5Password,
        userName: this.username
      }

      const response = await this.request(this.loginUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        data: JSON.stringify(loginData)
      })
      console.log('U号租登录响应:', JSON.stringify(response))
      if (response.success && response.object) {
        this.token = response.object
        // {{ AURA-X: Modify - 使用配置ID精确更新，避免误更新多个同类型配置. Approval: 寸止(ID:1735373000). }}
        // 更新数据库中的token
        const dbManager = new DatabaseManager()
        if (this.config._id) {
          // 使用配置ID精确更新
          await dbManager.updatePlatformConfigById(this.config._id, {
            token: this.token,
            login_status: 1,
            last_login_time: new Date()
          })
        } else {
          // 兼容旧版本：使用原有方法
          await dbManager.updatePlatformConfig(this.config.user_id, this.platformType, {
            token: this.token,
            login_status: 1,
            last_login_time: new Date()
          })
        }
        return {
          success: true,
          token: this.token,
          message: response.responseMsg || '登录成功'
        }
      } else {
        throw new Error(response.responseMsg || '登录失败')
      }
    } catch (error) {
      console.error('U号租登录失败:', error)
      // {{ AURA-X: Modify - 使用配置ID精确更新登录失效状态. Approval: 寸止(ID:1735373000). }}
      // 更新登录状态为失效
      const dbManager = new DatabaseManager()
      if (this.config._id) {
        // 使用配置ID精确更新
        await dbManager.updatePlatformConfigById(this.config._id, {
          login_status: 2
        })
      } else {
        // 兼容旧版本：使用原有方法
        await dbManager.updatePlatformConfig(this.config.user_id, this.platformType, {
          login_status: 2
        })
      }
      return {
        success: false,
        token: null,
        message: error.message
      }
    }
  }

  /**
   * 检查登录是否过期 (重写父类方法)
   * @param {Object} responseData 响应数据
   * @returns {boolean} 是否登录过期
   */
  isLoginExpired(responseData) {
    console.log('U号租登录状态检测:', responseData)
    // {{ AURA-X: Add - U号租登录状态检测逻辑. Approval: 寸止(ID:1735372900). }}
    return responseData.responseCode == 4024
  }

  // {{ AURA-X: Delete - 移除冗余方法，登录状态检测已移至基类统一处理. Approval: 寸止(ID:1735372900). }}
  // checkLoginStatus方法已移除，登录状态检测在基类的executeRequest中统一处理

  /**
   * 获取货架列表
   * @returns {Promise<Array>} 货架列表
   */
  async getShelfList() {
    try {
      const response = await this.request(`${this.baseUrl}/tool/goods/list/all`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'AccessToken': this.token
        },
        data: JSON.stringify({
          gameId: '',
          goodsStatus: '', // 空表示获取全部状态
          rentStatus: ''
        })
      })
      
      console.log('U号租货架列表响应:', JSON.stringify(response))
      
      if (response.success && response.object) {
        return response.object.map(item => this.convertShelfData(item))
      } else {
        throw new Error(response.responseMsg || '获取货架列表失败')
      }
    } catch (error) {
      console.error('获取U号租货架列表失败:', error)
      throw error
    }
  }

  // {{ AURA-X: Delete - 移除冗余方法，直接使用基类的getShelfStatusFromList. Approval: 寸止(ID:1735372900). }}
  // getShelfStatus方法已移除，直接使用基类的getShelfStatusFromList方法

  /**
   * 上架货架
   * @param {string} shelfId 货架ID
   * @returns {Promise<Object>} 操作结果
   */
  async onShelf(shelfId) {
    try {
      const response = await this.request(`${this.baseUrl}/tool/goods/status/update`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'AccessToken': this.token
        },
        data: JSON.stringify({
          goodsId: shelfId,
          status: 3 // 3表示上架
        })
      })
      
      if (response.success && response.object) {
        return {
          success: true,
          message: response.responseMsg || '上架成功'
        }
      } else {
        throw new Error(response.responseMsg || '上架失败')
      }
    } catch (error) {
      console.error('U号租上架失败:', error)
      return {
        success: false,
        message: error.message
      }
    }
  }

  /**
   * 下架货架
   * @param {string} shelfId 货架ID
   * @returns {Promise<Object>} 操作结果
   */
  async offShelf(shelfId) {
    try {
      const response = await this.request(`${this.baseUrl}/tool/goods/status/update`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'AccessToken': this.token
        },
        data: JSON.stringify({
          goodsId: shelfId,
          status: 4 // 4表示下架
        })
      })
      
      if (response.success && response.object) {
        return {
          success: true,
          message: response.responseMsg || '下架成功'
        }
      } else {
        throw new Error(response.responseMsg || '下架失败')
      }
    } catch (error) {
      console.error('U号租下架失败:', error)
      return {
        success: false,
        message: error.message
      }
    }
  }

  /**
   * 转换货架数据格式
   * @param {Object} rawData 原始数据
   * @returns {Object} 标准化数据
   */
  convertShelfData(rawData) {
    return {
      id: rawData.goodsId.toString(),
      game_account: rawData.gameAccount,
      game_name: rawData.gameName,
      game_role_name: rawData.gameRoleName,
      shelf_title: rawData.goodsTitle,
      rent_price: rawData.rentalByHour / 100, // U号租价格单位是分，需要转换为元
      min_rent_time: rawData.minRentTime,
      unified_state: StateManager.convertUhaoZuStatus(rawData.goodsStatus, rawData.rentStatus),
      platform_status: rawData
    }
  }

  /**
   * 转换状态码
   * U号租状态码：goodsStatus(3待租，4下架) + rentStatus(1待租，0出租中)
   * @param {Object} shelfData 货架数据
   * @returns {Object} 统一状态
   */
  convertStatus(shelfData) {
    return {
      unified_state: StateManager.convertUhaoZuStatus(shelfData.goods_status, shelfData.rent_status),
      platform_status: shelfData.platform_status
    }
  }

  /**
   * 构建表单数据
   * @param {Object} data 数据对象
   * @returns {string} 表单数据字符串
   */
  buildFormData(data) {
    const formData = new URLSearchParams()
    for (const key in data) {
      formData.append(key, data[key])
    }
    return formData.toString()
  }

  // {{ AURA-X: Delete - 移除重复的数据库操作，使用DatabaseManager统一处理. Approval: 寸止(ID:1735372900). }}
  // updatePlatformConfig方法已移除，使用DatabaseManager.updatePlatformConfig
}

module.exports = UhaoZuAdapter
