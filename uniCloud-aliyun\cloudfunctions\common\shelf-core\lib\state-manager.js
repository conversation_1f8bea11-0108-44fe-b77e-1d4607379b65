'use strict'

/**
 * 统一状态管理器
 * 负责处理所有状态相关的逻辑和转换
 */
class StateManager {
  /**
   * 统一状态定义
   */
  static STATES = {
    AVAILABLE: 0,    // 待租
    RENTED: 1,       // 出租中
    OFFLINE: -1,     // 下架
    OTHER: -2        // 其他
  }

  /**
   * 状态文本映射
   */
  static STATE_TEXTS = {
    [0]: '待租',
    [1]: '出租中',
    [-1]: '已下架',
    [-2]: '其他'
  }

  /**
   * 状态样式类映射
   */
  static STATE_CLASSES = {
    [0]: 'available',
    [1]: 'rented',
    [-1]: 'offline',
    [-2]: 'unknown'
  }

  /**
   * 租号玩状态转换
   * @param {number} ztStatus 租号玩状态码
   * @returns {number} 统一状态码
   */
  static convertZuhaoWanStatus(ztStatus) {
    switch (ztStatus) {
      case 0:
        return 0  // 待租
      case 1:
        return 1     // 出租中
      case -1:
        return -1    // 下架
      default:
        return -2      // 其他
    }
  }

  /**
   * U号租状态转换
   * @param {number} goodsStatus 商品状态
   * @param {number} rentStatus 租赁状态
   * @returns {number} 统一状态码
   */
  static convertUhaoZuStatus(goodsStatus, rentStatus) {
    if (goodsStatus === 4) {
      // 下架状态
      return -1
    } else if (goodsStatus === 3) {
      // 上架状态，需要根据租赁状态判断
      if (rentStatus === 0) {
        // 出租中
        return 1
      } else {
        // 待租
        return 0
      }
    } else {
      // 其他
      return -2
    }
  }

  /**
   * 获取状态文本
   * @param {number} state 统一状态码
   * @returns {string} 状态文本
   */
  static getStateText(state) {
    return StateManager.STATE_TEXTS[state] || '其他'
  }

  /**
   * 获取状态样式类
   * @param {number} state 统一状态码
   * @returns {string} 样式类名
   */
  static getStateClass(state) {
    return StateManager.STATE_CLASSES[state] || 'unknown'
  }

  /**
   * 检查状态是否可以上架
   * @param {number} state 统一状态码
   * @returns {boolean} 是否可以上架
   */
  static canOnShelf(state) {
    return state === -1
  }

  /**
   * 检查状态是否可以下架
   * @param {number} state 统一状态码
   * @returns {boolean} 是否可以下架
   */
  static canOffShelf(state) {
    return state === 0
  }

  /**
   * 获取操作按钮文本
   * @param {number} state 统一状态码
   * @returns {string} 按钮文本
   */
  static getActionButtonText(state) {
    if (StateManager.canOffShelf(state)) {
      return '下架'
    } else if (StateManager.canOnShelf(state)) {
      return '上架'
    } else {
      return '不可操作'
    }
  }

  /**
   * 获取目标状态
   * @param {number} currentState 当前状态
   * @returns {number} 目标状态
   */
  static getTargetState(currentState) {
    if (StateManager.canOffShelf(currentState)) {
      return -1
    } else if (StateManager.canOnShelf(currentState)) {
      return 0
    } else {
      throw new Error('当前状态不支持切换操作')
    }
  }

  /**
   * 验证状态码是否有效
   * @param {number} state 状态码
   * @returns {boolean} 是否有效
   */
  static isValidState(state) {
    return Object.values(StateManager.STATES).includes(state)
  }

  /**
   * 获取所有状态定义
   * @returns {Object} 状态定义对象
   */
  static getAllStates() {
    return { ...StateManager.STATES }
  }

  /**
   * 获取状态统计信息
   * @param {Array} shelfList 货架列表
   * @returns {Object} 统计信息
   */
  static getStateStats(shelfList) {
    const stats = {
      total: shelfList.length,
      available: 0,
      rented: 0,
      offline: 0,
      other: 0
    }

    shelfList.forEach(shelf => {
      switch (shelf.unified_state) {
        case 0:
          stats.available++
          break
        case 1:
          stats.rented++
          break
        case -1:
          stats.offline++
          break
        case -2:
          stats.other++
          break
      }
    })

    return stats
  }
}

module.exports = StateManager
