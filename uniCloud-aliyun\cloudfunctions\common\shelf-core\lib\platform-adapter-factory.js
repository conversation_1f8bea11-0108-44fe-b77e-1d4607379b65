'use strict'
// 导入各平台适配器
const <PERSON><PERSON><PERSON><PERSON>anAdapter = require('./adapters/zuhaowan-adapter')
const <PERSON><PERSON>ZuAdapter = require('./adapters/uhaozu-adapter')

/**
 * 平台适配器工厂类
 * 负责根据平台类型创建对应的适配器实例
 */
class PlatformAdapterFactory {
  /**
   * 创建平台适配器实例
   * @param {string} platformType 平台类型标识
   * @param {Object} config 平台配置信息
   * @returns {Object} 平台适配器实例
   */
  static create(platformType, config) {
    switch (platformType) {
      case 'zuhaowan':
        return new ZuhaoWanAdapter(config)
      case 'uhaozu':
        return new UhaoZuAdapter(config)
      default:
        throw new Error(`不支持的平台类型: ${platformType}`)
    }
  }

  /**
   * 获取支持的平台类型列表
   * @returns {Array} 支持的平台类型数组
   */
  static getSupportedPlatforms() {
    return [
      {
        type: 'zuhaowan',
        name: '租号玩',
        autoLogin: true,
        description: '支持账号密码自动登录'
      },
      {
        type: 'uhao<PERSON>',
        name: 'U号租',
        autoLogin: true,
        description: '支持账号密码自动登录'
      }
    ]
  }

  /**
   * 检查平台类型是否支持
   * @param {string} platformType 平台类型
   * @returns {boolean} 是否支持
   */
  static isSupported(platformType) {
    const supportedTypes = this.getSupportedPlatforms().map(p => p.type)
    return supportedTypes.includes(platformType)
  }
}

module.exports = PlatformAdapterFactory
