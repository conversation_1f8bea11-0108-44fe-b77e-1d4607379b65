import App from './App'
import Vue from 'vue'
import './uni.promisify.adaptor'
import uvUI from '@/uni_modules/uv-ui-tools'
import store from '@/store/index.js'
import cache from '@/common/js/minCache.js'
import utils from '@/common/js/utils.js'
import { loginInterceptor } from '@/utils/auth.js'
// #ifndef VUE3
Vue.config.productionTip = false
App.mpType = 'app'
Vue.prototype.$cache = cache
Vue.prototype.$utils = utils
Vue.prototype.$store = store
Vue.use(uvUI)
uni.$uv.setConfig({
  // 修改$uv.config对象的属性
  config: {
    // 修改默认单位为rpx，相当于执行 uni.$uv.config.unit = 'rpx'
    unit: 'rpx'
  }
})

// 添加登录拦截器
uni.addInterceptor('navigateTo', {
  invoke(args) {
    return loginInterceptor(args.url)
  }
})
uni.addInterceptor('redirectTo', {
  invoke(args) {
    return loginInterceptor(args.url)
  }
})
uni.addInterceptor('reLaunch', {
  invoke(args) {
    return loginInterceptor(args.url)
  }
})
uni.addInterceptor('switchTab', {
  invoke(args) {
    return loginInterceptor(args.url)
  }
})
const app = new Vue({
  store,
  ...App
})
app.$mount()
// #endif
// #ifdef H5

// #endif
// #ifdef VUE3
import { createSSRApp } from 'vue'
export function createApp() {
  const app = createSSRApp(App)
  app.use(uvUI)
  return {
    app
  }
}
// #endif
