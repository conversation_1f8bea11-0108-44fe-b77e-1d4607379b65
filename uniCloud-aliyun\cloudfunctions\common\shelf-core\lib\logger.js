'use strict'

/**
 * 日志记录器类
 * 负责记录操作日志到数据库
 */
class Logger {
  constructor() {
    this.db = uniCloud.database()
  }

  /**
   * 记录操作日志
   * @param {Object} logData 日志数据
   * @param {string} logData.user_id 用户ID
   * @param {string} logData.platform_type 平台类型
   * @param {string} logData.platform_shelf_id 平台货架ID
   * @param {string} logData.game_account 游戏账号
   * @param {string} logData.action 操作类型
   * @param {number} logData.status 操作状态
   * @param {string} logData.message 操作消息
   * @param {string} logData.error_code 错误代码
   * @param {Object} logData.request_data 请求数据
   * @param {Object} logData.response_data 响应数据
   * @param {number} logData.execution_time 执行时间
   * @param {string} logData.trigger_type 触发类型
   */
  async log(logData) {
    try {
      const logRecord = {
        user_id: logData.user_id || '',
        platform_type: logData.platform_type || '',
        platform_shelf_id: logData.platform_shelf_id || '',
        game_account: logData.game_account || '',
        action: logData.action || '',
        status: logData.status || 0,
        message: logData.message || '',
        error_code: logData.error_code || '',
        request_data: logData.request_data || null,
        response_data: logData.response_data || null,
        execution_time: logData.execution_time || 0,
        trigger_type: logData.trigger_type || 'auto',
        create_time: new Date()
      }

      await this.db.collection('operation-logs').add(logRecord)
      
      // 在控制台也输出日志
      console.log(`[${logRecord.action}] ${logRecord.platform_type} - ${logRecord.message}`)
    } catch (error) {
      console.error('记录日志失败:', error)
      // 日志记录失败不应该影响主流程，所以这里只打印错误不抛出异常
    }
  }

  /**
   * 记录成功日志
   * @param {Object} logData 日志数据
   */
  async logSuccess(logData) {
    await this.log({
      ...logData,
      status: 1
    })
  }

  /**
   * 记录失败日志
   * @param {Object} logData 日志数据
   */
  async logError(logData) {
    await this.log({
      ...logData,
      status: 0
    })
  }

  /**
   * 批量记录日志
   * @param {Array} logDataArray 日志数据数组
   */
  async logBatch(logDataArray) {
    try {
      const logRecords = logDataArray.map(logData => ({
        user_id: logData.user_id || '',
        platform_type: logData.platform_type || '',
        platform_shelf_id: logData.platform_shelf_id || '',
        game_account: logData.game_account || '',
        action: logData.action || '',
        status: logData.status || 0,
        message: logData.message || '',
        error_code: logData.error_code || '',
        request_data: logData.request_data || null,
        response_data: logData.response_data || null,
        execution_time: logData.execution_time || 0,
        trigger_type: logData.trigger_type || 'auto',
        create_time: new Date()
      }))

      if (logRecords.length > 0) {
        await this.db.collection('operation-logs').add(logRecords)
        console.log(`批量记录日志成功，共 ${logRecords.length} 条`)
      }
    } catch (error) {
      console.error('批量记录日志失败:', error)
    }
  }
}

module.exports = Logger
