/**
 * 云函数请求工具
 * 自动处理登录状态和错误处理
 */

import { getToken, toLogin, clearAuth } from './auth.js'

/**
 * 调用云函数
 * @param {string} name 云函数名称
 * @param {object} data 请求数据
 * @param {boolean} needAuth 是否需要登录验证
 */
export async function callFunction(name, data = {}, needAuth = true) {
  try {
    // 如果需要登录验证，添加token
    if (needAuth) {
      const token = getToken()
      if (!token) {
        uni.showToast({
          title: '未登录，请先登录',
          icon: 'none'
        })
        setTimeout(() => {
          clearAuth()
          toLogin()
        }, 1500)
        return Promise.reject(new Error('未登录'))
      }
      
      // 将token添加到请求头或数据中
      data._token = token
    }
    
    const result = await uniCloud.callFunction({
      name,
      data
    })
    
    // 检查返回结果
    if (result.result) {
      // 如果返回用户未登录错误，跳转到登录页
      if (result.result.code === -2) {
        uni.showToast({
          title: '登录已过期，请重新登录',
          icon: 'none',
        })
        setTimeout(() => {
          clearAuth()
          toLogin()
        }, 1500)
        return Promise.reject(new Error('登录已过期'))
      }
      
      return result.result
    }
    
    return result
  } catch (error) {
    console.error('云函数调用失败:', error)
    
    // 网络错误处理
    if (error.message.includes('timeout') || error.message.includes('network')) {
      uni.showToast({
        title: '网络连接超时',
        icon: 'none'
      })
    } else if (!error.message.includes('未登录') && !error.message.includes('登录已过期')) {
      uni.showToast({
        title: '请求失败，请重试',
        icon: 'none'
      })
    }
    return Promise.reject(error)
  }
}