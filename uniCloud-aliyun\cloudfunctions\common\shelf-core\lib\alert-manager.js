'use strict'
const axios = require('axios')

/**
 * 异常告警管理器
 * 支持Server酱告警方式
 */
class AlertManager {
  constructor(config = {}) {
    this.config = config
    this.serverChanKey = config.serverChanKey || ''

    // 告警频率限制 (避免告警轰炸)
    this.alertHistory = new Map()
    this.alertCooldown = 5 * 60 * 1000 // 5分钟冷却期
  }

  /**
   * 发送告警
   * @param {Object} alertData 告警数据
   * @param {string} alertData.title 告警标题
   * @param {string} alertData.message 告警内容
   * @param {string} alertData.level 告警级别: info, warning, error, critical
   * @param {string} alertData.source 告警来源
   * @param {Object} alertData.extra 额外数据
   */
  async sendAlert(alertData) {
    try {
      const { title, message, level = 'error', source = 'system', extra = {} } = alertData
      
      // 检查告警频率限制
      const alertKey = `${source}-${title}`
      if (this.shouldSkipAlert(alertKey)) {
        console.log(`告警被频率限制跳过: ${alertKey}`)
        return
      }

      // 记录告警历史
      this.alertHistory.set(alertKey, Date.now())

      // 构建告警内容
      const alertContent = this.buildAlertContent({
        title,
        message,
        level,
        source,
        extra,
        timestamp: new Date().toLocaleString('zh-CN')
      })

      // 发送Server酱告警
      if (this.serverChanKey) {
        await this.sendServerChan(alertContent)
      } else {
        console.warn('Server酱密钥未配置，跳过告警发送')
      }
      
      console.log(`告警发送完成: ${title}`)
    } catch (error) {
      console.error('发送告警失败:', error)
    }
  }

  /**
   * 检查是否应该跳过告警 (频率限制)
   * @param {string} alertKey 告警键
   * @returns {boolean} 是否跳过
   */
  shouldSkipAlert(alertKey) {
    const lastAlertTime = this.alertHistory.get(alertKey)
    if (!lastAlertTime) {
      return false
    }
    
    return (Date.now() - lastAlertTime) < this.alertCooldown
  }

  /**
   * 构建告警内容
   * @param {Object} data 告警数据
   * @returns {Object} 格式化的告警内容
   */
  buildAlertContent(data) {
    const levelEmoji = {
      info: 'ℹ️',
      warning: '⚠️',
      error: '❌',
      critical: '🚨'
    }

    return {
      title: `${levelEmoji[data.level] || '❌'} ${data.title}`,
      content: `**告警时间**: ${data.timestamp}
**告警级别**: ${data.level.toUpperCase()}
**告警来源**: ${data.source}
**告警内容**: ${data.message}

${data.extra && Object.keys(data.extra).length > 0 ? 
  `**详细信息**:
${Object.entries(data.extra).map(([k, v]) => `- ${k}: ${v}`).join('\n')}` : ''}`
    }
  }

  /**
   * 通过Server酱发送告警
   * @param {Object} alertContent 告警内容
   */
  async sendServerChan(alertContent) {
    try {
      const url = `https://sctapi.ftqq.com/${this.serverChanKey}.send`
      
      await axios.post(url, {
        title: alertContent.title,
        desp: alertContent.content
      }, {
        timeout: 10000
      })
      
      console.log('Server酱告警发送成功')
    } catch (error) {
      console.error('Server酱告警发送失败:', error.message)
    }
  }



  /**
   * 发送系统异常告警
   * @param {Error} error 异常对象
   * @param {string} source 异常来源
   * @param {Object} context 上下文信息
   */
  async alertSystemError(error, source = 'system', context = {}) {
    await this.sendAlert({
      title: '系统异常告警',
      message: error.message,
      level: 'error',
      source,
      extra: {
        stack: error.stack?.substring(0, 500) + '...',
        ...context
      }
    })
  }






}

module.exports = AlertManager
